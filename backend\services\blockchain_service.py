"""
Blockchain service for Web3 interactions with BASE Sepolia testnet
"""
import os
import json
import time
import hashlib
from typing import Optional, Dict, Any, Tuple
from web3 import Web3
from web3.exceptions import TransactionNotFound, TimeExhausted
import requests

from backend.config import (
    BLOCKCHAIN_RPC_URL, CONTRACT_ADDRESS, BASESCAN_API_KEY,
    DOCTOR_PRIVATE_KEY, PATIENT_PRIVATE_KEY, BUYER_PRIVATE_KEY,
    HOSPITAL_PRIVATE_KEY, GROUP_MANAGER_PRIVATE_KEY,
    DEFAULT_GAS_LIMIT, GAS_PRICE_MULTIPLIER
)


class BlockchainService:
    """Service for blockchain interactions"""
    
    def __init__(self):
        """Initialize blockchain service"""
        self.w3 = None
        self.contract = None
        self.contract_abi = []
        self._initialize_web3()
        self._load_contract()
    
    def _initialize_web3(self):
        """Initialize Web3 connection"""
        try:
            # Use the RPC URL from environment
            rpc_url = os.getenv('SEPOLIA_RPC_URL', BLOCKCHAIN_RPC_URL)
            
            if rpc_url.startswith('wss://') or rpc_url.startswith('ws://'):
                # WebSocket provider
                self.w3 = Web3(Web3.WebsocketProvider(
                    rpc_url,
                    websocket_kwargs={
                        'timeout': 180,
                        'ping_interval': 30,
                        'ping_timeout': 10,
                        'max_size': 10 * 1024 * 1024
                    }
                ))
            else:
                # HTTP provider
                self.w3 = Web3(Web3.HTTPProvider(
                    rpc_url,
                    request_kwargs={
                        'timeout': 300,
                        'headers': {
                            "Content-Type": "application/json",
                            "User-Agent": "healthcare-data-sharing/1.0"
                        }
                    }
                ))
            
            if self.w3.is_connected():
                print(f"✅ Connected to blockchain at {rpc_url}")
            else:
                print(f"❌ Failed to connect to blockchain at {rpc_url}")
                
        except Exception as e:
            print(f"❌ Error initializing Web3: {str(e)}")
            self.w3 = None
    
    def _load_contract(self):
        """Load contract ABI and create contract instance"""
        try:
            # Try to load contract ABI from various locations
            abi_paths = [
                "artifacts/contracts/DataHub.sol/DataHub.json",
                "contracts/DataHub.json",
                "../contracts/DataHub.json",
                "./contracts/DataHub.json"
            ]
            
            contract_abi = []
            for abi_path in abi_paths:
                if os.path.exists(abi_path):
                    with open(abi_path, "r") as f:
                        contract_json = json.load(f)
                        contract_abi = contract_json.get("abi", [])
                    print(f"✅ Loaded contract ABI from {abi_path}")
                    break
            
            if not contract_abi:
                print("⚠️ Contract ABI not found, using empty ABI")
            
            self.contract_abi = contract_abi
            
            if self.w3 and CONTRACT_ADDRESS:
                self.contract = self.w3.eth.contract(
                    address=self.w3.to_checksum_address(CONTRACT_ADDRESS),
                    abi=contract_abi
                )
                print(f"✅ Contract initialized at {CONTRACT_ADDRESS}")
            
        except Exception as e:
            print(f"❌ Error loading contract: {str(e)}")
            self.contract = None
    
    def is_connected(self) -> bool:
        """Check if connected to blockchain"""
        return self.w3 is not None and self.w3.is_connected()
    
    def get_gas_price(self) -> int:
        """Get current gas price from Basescan API or Web3"""
        try:
            # Try Basescan API first
            if BASESCAN_API_KEY:
                url = f"https://api.basescan.org/api?module=gastracker&action=gasoracle&apikey={BASESCAN_API_KEY}"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "1":
                        # Convert Gwei to Wei
                        gas_price_gwei = float(data["result"]["ProposeGasPrice"])
                        gas_price_wei = int(gas_price_gwei * 1e9)
                        print(f"📊 Gas price from Basescan: {gas_price_gwei} Gwei")
                        return gas_price_wei
            
            # Fallback to Web3
            if self.w3:
                gas_price = self.w3.eth.gas_price
                print(f"📊 Gas price from Web3: {self.w3.from_wei(gas_price, 'gwei')} Gwei")
                return gas_price
            
            # Default fallback
            return 1000000000  # 1 Gwei
            
        except Exception as e:
            print(f"⚠️ Error getting gas price: {str(e)}")
            return 1000000000  # 1 Gwei fallback
    
    def get_nonce(self, address: str) -> int:
        """Get nonce for address"""
        if not self.w3:
            return 0
        try:
            return self.w3.eth.get_transaction_count(address)
        except Exception as e:
            print(f"⚠️ Error getting nonce for {address}: {str(e)}")
            return 0
    
    def estimate_gas(self, transaction: Dict[str, Any]) -> int:
        """Estimate gas for transaction"""
        if not self.w3 or not self.contract:
            return DEFAULT_GAS_LIMIT
        
        try:
            estimated = self.w3.eth.estimate_gas(transaction)
            # Add 20% buffer
            return int(estimated * 1.2)
        except Exception as e:
            print(f"⚠️ Error estimating gas: {str(e)}")
            return DEFAULT_GAS_LIMIT
    
    def send_transaction(self, transaction: Dict[str, Any], private_key: str) -> Optional[str]:
        """Send transaction to blockchain"""
        if not self.w3:
            print("❌ Web3 not connected")
            return None
        
        try:
            # Sign transaction
            signed_tx = self.w3.eth.account.sign_transaction(transaction, private_key)
            
            # Send transaction
            tx_hash = self.w3.eth.send_raw_transaction(signed_tx.raw_transaction)
            tx_hash_hex = tx_hash.hex()
            
            print(f"📤 Transaction sent: {tx_hash_hex}")
            print(f"🔗 View on Basescan: https://sepolia.basescan.org/tx/{tx_hash_hex}")
            
            return tx_hash_hex
            
        except Exception as e:
            error_str = str(e)
            print(f"❌ Error sending transaction: {error_str}")
            
            # Handle specific errors
            if 'replacement transaction underpriced' in error_str:
                print("💡 Try increasing gas price")
            elif 'insufficient funds' in error_str:
                print("💡 Insufficient balance for transaction")
            
            return None
    
    def wait_for_receipt(self, tx_hash: str, timeout: int = 120) -> Optional[Dict[str, Any]]:
        """Wait for transaction receipt"""
        if not self.w3:
            return None
        
        try:
            print(f"⏳ Waiting for transaction receipt: {tx_hash}")
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=timeout)
            
            if receipt.status == 1:
                print(f"✅ Transaction confirmed in block {receipt.blockNumber}")
                print(f"⛽ Gas used: {receipt.gasUsed}")
            else:
                print(f"❌ Transaction failed: {receipt}")
            
            return dict(receipt)
            
        except TimeExhausted:
            print(f"⏰ Transaction timeout after {timeout} seconds")
            return None
        except Exception as e:
            print(f"❌ Error waiting for receipt: {str(e)}")
            return None
    
    def store_data(self, cid: str, merkle_root: str, signature: str, from_address: str, private_key: str) -> Optional[str]:
        """Store data on blockchain using storeData function"""
        if not self.contract:
            print("❌ Contract not available")
            return self._generate_mock_hash(cid, merkle_root)
        
        try:
            # Convert parameters to bytes32
            cid_bytes32 = self.w3.to_bytes(hexstr=hashlib.sha256(cid.encode()).hexdigest())
            merkle_root_bytes32 = self.w3.to_bytes(hexstr=merkle_root)
            signature_bytes = signature.encode() if isinstance(signature, str) else signature
            
            # Get gas price and nonce
            gas_price = int(self.get_gas_price() * GAS_PRICE_MULTIPLIER)
            nonce = self.get_nonce(from_address)
            
            # Build transaction
            transaction = self.contract.functions.storeData(
                cid_bytes32, merkle_root_bytes32, signature_bytes
            ).build_transaction({
                'from': from_address,
                'gas': DEFAULT_GAS_LIMIT,
                'gasPrice': gas_price,
                'nonce': nonce,
            })
            
            # Send transaction
            return self.send_transaction(transaction, private_key)
            
        except Exception as e:
            print(f"❌ Error in store_data: {str(e)}")
            return self._generate_mock_hash(cid, merkle_root)
    
    def _generate_mock_hash(self, *args) -> str:
        """Generate mock transaction hash for fallback"""
        data = "_".join(str(arg) for arg in args) + f"_{int(time.time())}"
        return f"0x{hashlib.sha256(data.encode()).hexdigest()}"


# Global service instance
_blockchain_service: Optional[BlockchainService] = None

def get_blockchain_service() -> BlockchainService:
    """Get or create blockchain service instance"""
    global _blockchain_service
    if _blockchain_service is None:
        _blockchain_service = BlockchainService()
    return _blockchain_service
