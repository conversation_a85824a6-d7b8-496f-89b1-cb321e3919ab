"""
Configuration and constants for the healthcare data sharing system
"""
import os
from typing import Dict, Any

# Environment variables with defaults
PATIENT_ADDRESS = os.getenv('PATIENT_ADDRESS', '')
DOCTOR_ADDRESS = os.getenv('DOCTOR_ADDRESS', '')
HOSPITAL_ADDRESS = os.getenv('HOSPITAL_ADDRESS', '')
GROUP_MANAGER_ADDRESS = os.getenv('GROUP_MANAGER_ADDRESS', '')
REVOCATION_MANAGER_ADDRESS = os.getenv('REVOCATION_MANAGER_ADDRESS', '')
BUYER_ADDRESS = os.getenv('BUYER_ADDRESS', '')

# Blockchain configuration
BLOCKCHAIN_RPC_URL = os.getenv('BLOCKCHAIN_RPC_URL', 'wss://base-sepolia-rpc.publicnode.com')
SEPOLIA_RPC_URL = os.getenv('SEPOLIA_RPC_URL', 'wss://base-sepolia-rpc.publicnode.com')
CONTRACT_ADDRESS = os.getenv('CONTRACT_ADDRESS', '')
PRIVATE_KEY = os.getenv('PRIVATE_KEY', '')

# Private keys for each role (for demo purposes)
DOCTOR_PRIVATE_KEY = os.getenv('DOCTOR_PRIVATE_KEY', '')
PATIENT_PRIVATE_KEY = os.getenv('PATIENT_PRIVATE_KEY', '')
HOSPITAL_PRIVATE_KEY = os.getenv('HOSPITAL_PRIVATE_KEY', '')
BUYER_PRIVATE_KEY = os.getenv('BUYER_PRIVATE_KEY', '')
GROUP_MANAGER_PRIVATE_KEY = os.getenv('GROUP_MANAGER_PRIVATE_KEY', '')
REVOCATION_MANAGER_PRIVATE_KEY = os.getenv('REVOCATION_MANAGER_PRIVATE_KEY', '')

# IPFS configuration
IPFS_URL = os.getenv('IPFS_URL', 'http://127.0.0.1:5001')
IPFS_API_HOST = os.getenv('IPFS_API_HOST', '127.0.0.1')
IPFS_API_PORT = os.getenv('IPFS_API_PORT', '5001')

# API configuration
API_HOST = os.getenv('API_HOST', '0.0.0.0')
API_PORT = int(os.getenv('API_PORT', '8000'))
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# Security configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# File storage paths
LOCAL_STORAGE_PATH = os.getenv('LOCAL_STORAGE_PATH', 'local_storage')
SECURE_KEYS_PATH = os.getenv('SECURE_KEYS_PATH', 'secure_keys')
TRANSACTION_LOG_PATH = os.getenv('TRANSACTION_LOG_PATH', 'transaction_log.txt')

# Gas configuration
DEFAULT_GAS_LIMIT = int(os.getenv('DEFAULT_GAS_LIMIT', '2000000'))
GAS_PRICE_MULTIPLIER = float(os.getenv('GAS_PRICE_MULTIPLIER', '1.2'))

# API Keys
BASESCAN_API_KEY = os.getenv('BASESCAN_API_KEY', 'I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y')

# Role definitions
ROLES = {
    "PATIENT": "patient",
    "DOCTOR": "doctor", 
    "HOSPITAL": "hospital",
    "GROUP_MANAGER": "group_manager",
    "REVOCATION_MANAGER": "revocation_manager",
    "BUYER": "buyer"
}

# Address to role mapping
ADDRESS_ROLE_MAPPING = {
    PATIENT_ADDRESS.lower(): ROLES["PATIENT"],
    DOCTOR_ADDRESS.lower(): ROLES["DOCTOR"],
    HOSPITAL_ADDRESS.lower(): ROLES["HOSPITAL"],
    GROUP_MANAGER_ADDRESS.lower(): ROLES["GROUP_MANAGER"],
    REVOCATION_MANAGER_ADDRESS.lower(): ROLES["REVOCATION_MANAGER"],
    BUYER_ADDRESS.lower(): ROLES["BUYER"]
}

# Response templates
def success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Standard success response format"""
    response = {
        "success": True,
        "message": message
    }
    if data is not None:
        response["data"] = data
    return response

def error_response(message: str, status_code: int = 500) -> Dict[str, Any]:
    """Standard error response format"""
    from fastapi import HTTPException
    raise HTTPException(status_code=status_code, detail=message)

# Ensure required directories exist
def ensure_directories():
    """Create required directories if they don't exist"""
    directories = [LOCAL_STORAGE_PATH, SECURE_KEYS_PATH, f"{LOCAL_STORAGE_PATH}/purchases"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# Initialize directories on import
ensure_directories()
