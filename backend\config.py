"""
Configuration and constants for the healthcare data sharing system
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Environment variables with defaults from .env file
PATIENT_ADDRESS = os.getenv('PATIENT_ADDRESS', '0xEDB64f85F1fC9357EcA100C2970f7F84a5faAD4A')
DOCTOR_ADDRESS = os.getenv('DOCTOR_ADDRESS', '******************************************')
HOSPITAL_ADDRESS = os.getenv('HOSPITAL_ADDRESS', '******************************************')
GROUP_MANAGER_ADDRESS = os.getenv('GROUP_MANAGER_ADDRESS', '******************************************')
REVOCATION_MANAGER_ADDRESS = os.getenv('REVOCATION_MANAGER_ADDRESS', '******************************************')
BUYER_ADDRESS = os.getenv('BUYER_ADDRESS', '******************************************')
WALLET_ADDRESS = os.getenv('WALLET_ADDRESS', '******************************************')

# Blockchain configuration
BLOCKCHAIN_RPC_URL = os.getenv('BLOCKCHAIN_RPC_URL', 'https://base-sepolia-rpc.publicnode.com')
SEPOLIA_RPC_URL = os.getenv('SEPOLIA_RPC_URL', 'https://base-sepolia-rpc.publicnode.com')
CONTRACT_ADDRESS = os.getenv('CONTRACT_ADDRESS', '******************************************')
PRIVATE_KEY = os.getenv('PRIVATE_KEY', '91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e')

# Private keys for each role (for demo purposes)
DOCTOR_PRIVATE_KEY = os.getenv('DOCTOR_PRIVATE_KEY', '03d2a71ec5d40078a8c7552c795f925474129929b7050da52d2a95af30bc0cec')
PATIENT_PRIVATE_KEY = os.getenv('PATIENT_PRIVATE_KEY', '91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e')
HOSPITAL_PRIVATE_KEY = os.getenv('HOSPITAL_PRIVATE_KEY', '1e291b59ddd32689ee42459971d5f0ad1b794972be116e5fb9f1929616afeb47')
BUYER_PRIVATE_KEY = os.getenv('BUYER_PRIVATE_KEY', 'e25e8f9128ba1bef33e1cacb2e1b50dd3f34c7f175b61098b4ab4f17c9416d06')
GROUP_MANAGER_PRIVATE_KEY = os.getenv('GROUP_MANAGER_PRIVATE_KEY', '59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d')
REVOCATION_MANAGER_PRIVATE_KEY = os.getenv('REVOCATION_MANAGER_PRIVATE_KEY', '4bf1c7cac1c53c7f7f7ddcc979b159d66a3d2d721fa4053330adbb100be628a0')

# IPFS configuration
IPFS_URL = os.getenv('IPFS_URL', 'http://127.0.0.1:5001')
IPFS_API_HOST = os.getenv('IPFS_API_HOST', '127.0.0.1')
IPFS_API_PORT = os.getenv('IPFS_API_PORT', '5001')

# API configuration
API_HOST = os.getenv('API_HOST', '0.0.0.0')
API_PORT = int(os.getenv('API_PORT', '8000'))
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# Security configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# File storage paths
LOCAL_STORAGE_PATH = os.getenv('LOCAL_STORAGE_PATH', 'local_storage')
SECURE_KEYS_PATH = os.getenv('SECURE_KEYS_PATH', 'secure_keys')
TRANSACTION_LOG_PATH = os.getenv('TRANSACTION_LOG_PATH', 'transaction_log.txt')

# Gas configuration
DEFAULT_GAS_LIMIT = int(os.getenv('DEFAULT_GAS_LIMIT', '2000000'))
GAS_PRICE_MULTIPLIER = float(os.getenv('GAS_PRICE_MULTIPLIER', '1.2'))

# API Keys
BASESCAN_API_KEY = os.getenv('BASESCAN_API_KEY', 'I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y')
ETHERSCAN_API_KEY = os.getenv('ETHERSCAN_API_KEY', '**********************************')

# Coinbase Cloud configuration (optional)
COINBASE_PROJECT_ID = os.getenv('COINBASE_PROJECT_ID', '05614f86-3dbd-45d9-be9d-69ceeb939336')
COINBASE_API_KEY_ID = os.getenv('COINBASE_API_KEY_ID', 'e89240c9-3a16-4777-896c-e702a70ff34a')
COINBASE_API_KEY_SECRET = os.getenv('COINBASE_API_KEY_SECRET', 'Id9CXHM6392XOICcgrSx2YYdZdT6WqysI6cVh1PoDMq5dp5cx01woBsa9Y4xq3s2fHGNuwmZn9PiVJLlg96WDQ==')
COINBASE_CLIENT_API_KEY = os.getenv('COINBASE_CLIENT_API_KEY', 'TU79b5nxSoHEPVmNhElKsyBqt9CUbNTf')

# Role definitions
ROLES = {
    "PATIENT": "patient",
    "DOCTOR": "doctor", 
    "HOSPITAL": "hospital",
    "GROUP_MANAGER": "group_manager",
    "REVOCATION_MANAGER": "revocation_manager",
    "BUYER": "buyer"
}

# Address to role mapping
ADDRESS_ROLE_MAPPING = {
    PATIENT_ADDRESS.lower(): ROLES["PATIENT"],
    DOCTOR_ADDRESS.lower(): ROLES["DOCTOR"],
    HOSPITAL_ADDRESS.lower(): ROLES["HOSPITAL"],
    GROUP_MANAGER_ADDRESS.lower(): ROLES["GROUP_MANAGER"],
    REVOCATION_MANAGER_ADDRESS.lower(): ROLES["REVOCATION_MANAGER"],
    BUYER_ADDRESS.lower(): ROLES["BUYER"]
}

# Response templates
def success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Standard success response format"""
    response = {
        "success": True,
        "message": message
    }
    if data is not None:
        response["data"] = data
    return response

def error_response(message: str, status_code: int = 500) -> Dict[str, Any]:
    """Standard error response format"""
    from fastapi import HTTPException
    raise HTTPException(status_code=status_code, detail=message)

# Validation function for environment variables
def validate_config():
    """Validate that all required environment variables are loaded"""
    required_vars = {
        'PATIENT_ADDRESS': PATIENT_ADDRESS,
        'DOCTOR_ADDRESS': DOCTOR_ADDRESS,
        'HOSPITAL_ADDRESS': HOSPITAL_ADDRESS,
        'GROUP_MANAGER_ADDRESS': GROUP_MANAGER_ADDRESS,
        'REVOCATION_MANAGER_ADDRESS': REVOCATION_MANAGER_ADDRESS,
        'BUYER_ADDRESS': BUYER_ADDRESS,
        'CONTRACT_ADDRESS': CONTRACT_ADDRESS,
        'SEPOLIA_RPC_URL': SEPOLIA_RPC_URL
    }

    missing_vars = []
    for var_name, var_value in required_vars.items():
        if not var_value or var_value == '':
            missing_vars.append(var_name)

    if missing_vars:
        print(f"⚠️ Warning: Missing environment variables: {', '.join(missing_vars)}")
        print("📝 Please check your .env file")
    else:
        print("✅ All required environment variables loaded successfully")

    # Print loaded configuration (without private keys)
    print(f"🔗 Blockchain RPC: {SEPOLIA_RPC_URL}")
    print(f"📄 Contract Address: {CONTRACT_ADDRESS}")
    print(f"👤 Patient Address: {PATIENT_ADDRESS}")
    print(f"👨‍⚕️ Doctor Address: {DOCTOR_ADDRESS}")
    print(f"🏥 Hospital Address: {HOSPITAL_ADDRESS}")
    print(f"💰 Buyer Address: {BUYER_ADDRESS}")

# Ensure required directories exist
def ensure_directories():
    """Create required directories if they don't exist"""
    directories = [
        LOCAL_STORAGE_PATH,
        SECURE_KEYS_PATH,
        f"{LOCAL_STORAGE_PATH}/purchases",
        f"{LOCAL_STORAGE_PATH}/transactions",
        f"{LOCAL_STORAGE_PATH}/storing_transactions",
        f"{LOCAL_STORAGE_PATH}/sharing_transactions",
        f"{LOCAL_STORAGE_PATH}/purchasing_transactions",
        f"{LOCAL_STORAGE_PATH}/other_transactions",
        f"{LOCAL_STORAGE_PATH}/revocations"
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# Initialize directories and validate config on import
ensure_directories()
validate_config()
