"""
Medical records endpoints
"""
import hashlib
import time
from fastapi import APIRout<PERSON>, Body, Request, Depends
from typing import Dict, Any

from backend.models.records import RecordSign, RecordStore, RecordRetrieve, RecordResponse
from backend.config import success_response, error_response, DOCTOR_ADDRESS, PATIENT_ADDRESS
from backend.data import MerkleService, encrypt_record, decrypt_record
from backend.groupsig_utils import sign_message, verify_signature
from backend.services.key_manager import KeyManager
from backend.services.ipfs_service import IPFSService
from backend.services.blockchain_service import get_blockchain_service

router = APIRouter()

# Import dependency functions from main
def get_key_manager() -> KeyManager:
    """Dependency to get key manager"""
    from backend.main import get_key_manager as _get_key_manager
    return _get_key_manager()

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service"""
    from backend.main import get_ipfs_service as _get_ipfs_service
    return _get_ipfs_service()

@router.post("/sign")
async def sign_record(
    record_data: Dict[str, Any] = Body(...),
    wallet_address: str = Body(...),
    key_manager: KeyManager = Depends(get_key_manager)
):
    """
    Doctor creates and signs a record, which is then returned to be encrypted and stored.
    """
    try:
        # Check if the wallet address matches the Doctor address
        if wallet_address == DOCTOR_ADDRESS:
            print(f"✅ Doctor {wallet_address} is signing a record")
        else:
            print(f"⚠️ Non-doctor address {wallet_address} is attempting to sign a record")

        # Validate the record data
        if not record_data:
            return error_response("Record data is required", 400)

        # Create Merkle tree from the record data
        merkle_service = MerkleService()
        merkle_root, proofs = merkle_service.create_merkle_tree(record_data)

        # Sign the merkle root with group signature using the doctor's member key
        signature = sign_message(merkle_root)

        # If group signature fails, fall back to a mock signature
        if signature is None:
            print("⚠️ Group signature failed. Using mock signature.")
            signature = hashlib.sha256(f"{merkle_root}_{int(time.time())}".encode()).hexdigest()

        return success_response(
            data={
                "record": record_data,
                "merkleRoot": merkle_root,
                "proofs": proofs,
                "signature": signature
            }
        )
    except Exception as e:
        return error_response(str(e), 500)

@router.post("/store")
async def store_record(
    data: Dict[str, Any] = Body(...),
    ipfs_service: IPFSService = Depends(get_ipfs_service),
    key_manager: KeyManager = Depends(get_key_manager)
):
    """
    Store an encrypted record on IPFS and register it on the blockchain
    """
    try:
        # Extract data
        record = data.get("record", {})
        signature = data.get("signature", "")
        merkle_root = data.get("merkleRoot", "")
        patient_address = data.get("patientAddress", "")
        hospital_info = data.get("hospitalInfo", "General Hospital")

        print(f"📝 Storing record for patient {patient_address}")

        # Validate inputs
        if not record or not signature or not merkle_root or not patient_address:
            return error_response("Missing required fields", 400)

        # Generate patient key deterministically (in production, use secure key derivation)
        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()

        # Encrypt the record with the patient's key
        encrypted_record = encrypt_record(record, patient_key)
        print(f"🔒 Encrypted record: {len(encrypted_record)} bytes")

        # Store encrypted record on IPFS
        cid = ipfs_service.add_data(encrypted_record)
        print(f"📦 Stored on IPFS with CID: {cid}")

        # Create eId (encrypted hospital info and patient key)
        # In production, this would use PCS encryption with Group Manager's public key
        eId = f"mock_eid_{hashlib.sha256(f'{hospital_info}_{patient_key.hex()}_{int(time.time())}'.encode()).hexdigest()}"

        # Get blockchain service and attempt real transaction
        blockchain_service = get_blockchain_service()

        if blockchain_service.is_connected():
            print("🔗 Attempting blockchain transaction...")

            # Get doctor's private key for signing
            import os
            doctor_private_key = os.getenv('DOCTOR_PRIVATE_KEY')

            if doctor_private_key:
                # Attempt real blockchain transaction
                tx_hash = blockchain_service.store_data(
                    cid=cid,
                    merkle_root=merkle_root,
                    signature=signature,
                    from_address=DOCTOR_ADDRESS,
                    private_key=doctor_private_key
                )

                if tx_hash and tx_hash.startswith('0x') and len(tx_hash) == 66:
                    print(f"✅ Real blockchain transaction: {tx_hash}")

                    # Wait for receipt (optional, can be done async)
                    receipt = blockchain_service.wait_for_receipt(tx_hash, timeout=60)

                    gas_used = receipt.get('gasUsed', 200000) if receipt else 200000
                    gas_price = blockchain_service.get_gas_price()

                    return success_response(
                        data={
                            "cid": cid,
                            "merkleRoot": merkle_root,
                            "eId": eId,
                            "txHash": tx_hash,
                            "gasUsed": gas_used,
                            "gasPrice": gas_price,
                            "gasPriceGwei": gas_price / 1e9,
                            "simulated": False,
                            "receipt": receipt
                        }
                    )
                else:
                    print("⚠️ Blockchain transaction failed, using mock hash")
            else:
                print("⚠️ Doctor private key not found, using mock transaction")
        else:
            print("⚠️ Blockchain not connected, using mock transaction")

        # Fallback to mock transaction
        tx_hash = f"0x{hashlib.sha256(f'{cid}_{merkle_root}_{int(time.time())}'.encode()).hexdigest()}"

        return success_response(
            data={
                "cid": cid,
                "merkleRoot": merkle_root,
                "eId": eId,
                "txHash": tx_hash,
                "gasUsed": 200000,  # Estimated
                "gasPrice": 10000000000,  # 10 Gwei
                "gasPriceGwei": 10.0,
                "simulated": True
            }
        )
    except Exception as e:
        return error_response(str(e), 500)

@router.get("/list")
async def list_patient_records(
    patient_address: str,
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    List all records for a patient
    """
    try:
        # Check if the wallet address matches the Patient address
        if patient_address == PATIENT_ADDRESS:
            print(f"✅ Patient {patient_address} is listing their records")
        else:
            print(f"⚠️ Non-patient address {patient_address} is attempting to list records")

        records = []

        # Check local storage for records
        import os
        from backend.config import LOCAL_STORAGE_PATH

        if os.path.exists(LOCAL_STORAGE_PATH):
            for filename in os.listdir(LOCAL_STORAGE_PATH):
                file_path = os.path.join(LOCAL_STORAGE_PATH, filename)
                if os.path.isfile(file_path):
                    try:
                        # Try to decrypt the record
                        with open(file_path, "rb") as f:
                            encrypted_record = f.read()

                        # Generate the patient's key deterministically
                        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()

                        # Decrypt the record (for API response, keep binary data as base64)
                        decrypted_record = decrypt_record(encrypted_record, patient_key, for_api_response=True)

                        # Check if this record belongs to this patient
                        patient_id = decrypted_record.get("patientId") or decrypted_record.get("patientID")

                        if patient_id == patient_address:
                            # Add metadata to the record
                            decrypted_record["cid"] = filename
                            decrypted_record["timestamp"] = os.path.getmtime(file_path)
                            records.append(decrypted_record)
                            print(f"✅ Added record {filename} to patient's records")
                    except Exception as e:
                        # Skip records that can't be decrypted with this patient's key
                        print(f"⚠️ Skipping record {filename}: {str(e)}")

        return success_response(data=records)
    except Exception as e:
        return error_response(str(e), 500)

@router.post("/retrieve")
async def retrieve_record(
    data: Dict[str, Any] = Body(...),
    ipfs_service: IPFSService = Depends(get_ipfs_service),
    key_manager: KeyManager = Depends(get_key_manager)
):
    """
    Patient retrieves and decrypts a record using their key
    """
    try:
        # Extract data
        cid = data.get("cid", "")
        signature = data.get("signature", "")
        eId = data.get("eId", "")
        patient_address = data.get("patientAddress", "")

        # Validate inputs
        if not cid or not patient_address or not eId or not signature:
            return error_response("Missing required fields", 400)

        print(f"📖 Patient {patient_address} retrieving record {cid}")

        # Generate merkle_root deterministically for demo
        merkle_root = hashlib.sha256(f"{cid}_merkle_root".encode()).hexdigest()

        # Verify the signature
        signature_verified = verify_signature(merkle_root, signature)
        if not signature_verified:
            print(f"❌ Signature verification failed")
            return error_response("Invalid signature", 400)

        print(f"✅ Signature verified successfully")

        # Retrieve the encrypted record
        encrypted_record = ipfs_service.get_data(cid)
        if not encrypted_record:
            return error_response("Record not found", 404)

        # Generate patient key (in production, derive securely)
        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()

        # Decrypt the record (for API response, keep binary data as base64)
        decrypted_record = decrypt_record(encrypted_record, patient_key, for_api_response=True)

        return success_response(data=decrypted_record)
    except Exception as e:
        return error_response(str(e), 500)
