# ======================================================
# HEALTHCARE DATA SHARING SYSTEM - ENVIRONMENT VARIABLES
# ======================================================
# Copy this file to .env and fill in your actual values
# WARNING: Never commit .env file with real private keys to version control

# ======================================================
# BLOCKCHAIN CONFIGURATION
# ======================================================

# Base Sepolia testnet connection
SEPOLIA_RPC_URL=https://base-sepolia-rpc.publicnode.com
BLOCKCHAIN_RPC_URL=https://base-sepolia-rpc.publicnode.com
CONTRACT_ADDRESS=******************************************

# ======================================================
# ACCOUNT ADDRESSES
# ======================================================

# Default account (used for deployment)
WALLET_ADDRESS=******************************************

# Role-specific addresses (update with your actual addresses)
PATIENT_ADDRESS=******************************************
DOCTOR_ADDRESS=******************************************
HOSPITAL_ADDRESS=******************************************
BUYER_ADDRESS=******************************************
GROUP_MANAGER_ADDRESS=******************************************
REVOCATION_MANAGER_ADDRESS=******************************************

# ======================================================
# PRIVATE KEYS
# ======================================================
# WARNING: Never commit real private keys to version control
# These are test keys for development only

# Default private key (used for deployment)
PRIVATE_KEY=91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e

# Role-specific private keys (update with your actual private keys)
PATIENT_PRIVATE_KEY=91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e
DOCTOR_PRIVATE_KEY=03d2a71ec5d40078a8c7552c795f925474129929b7050da52d2a95af30bc0cec
HOSPITAL_PRIVATE_KEY=1e291b59ddd32689ee42459971d5f0ad1b794972be116e5fb9f1929616afeb47
BUYER_PRIVATE_KEY=e25e8f9128ba1bef33e1cacb2e1b50dd3f34c7f175b61098b4ab4f17c9416d06
GROUP_MANAGER_PRIVATE_KEY=59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d
REVOCATION_MANAGER_PRIVATE_KEY=4bf1c7cac1c53c7f7f7ddcc979b159d66a3d2d721fa4053330adbb100be628a0

# ======================================================
# IPFS CONFIGURATION
# ======================================================

# Local IPFS node configuration
IPFS_URL=http://127.0.0.1:5001
IPFS_API_HOST=127.0.0.1
IPFS_API_PORT=5001

# ======================================================
# API CONFIGURATION
# ======================================================

# API server configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=False

# Security configuration
SECRET_KEY=your-secret-key-here-change-this-in-production

# ======================================================
# FILE STORAGE PATHS
# ======================================================

# Local storage paths
LOCAL_STORAGE_PATH=local_storage
SECURE_KEYS_PATH=secure_keys
TRANSACTION_LOG_PATH=transaction_log.txt

# ======================================================
# GAS CONFIGURATION
# ======================================================

# Gas settings for blockchain transactions
DEFAULT_GAS_LIMIT=2000000
GAS_PRICE_MULTIPLIER=1.2

# ======================================================
# API KEYS
# ======================================================

# Basescan API key for BASE Sepolia testnet
BASESCAN_API_KEY=I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y

# Etherscan API key (if needed)
ETHERSCAN_API_KEY=**********************************

# ======================================================
# COINBASE CLOUD (OPTIONAL)
# ======================================================

# Coinbase Cloud credentials (if using Coinbase Cloud services)
COINBASE_PROJECT_ID=05614f86-3dbd-45d9-be9d-69ceeb939336
COINBASE_API_KEY_ID=e89240c9-3a16-4777-896c-e702a70ff34a
COINBASE_API_KEY_SECRET=Id9CXHM6392XOICcgrSx2YYdZdT6WqysI6cVh1PoDMq5dp5cx01woBsa9Y4xq3s2fHGNuwmZn9PiVJLlg96WDQ==
COINBASE_CLIENT_API_KEY=TU79b5nxSoHEPVmNhElKsyBqt9CUbNTf

# ======================================================
# DEVELOPMENT NOTES
# ======================================================
#
# 1. Copy this file to .env: cp .env.example .env
# 2. Update the values with your actual configuration
# 3. Make sure .env is in your .gitignore file
# 4. For production, use secure key management systems
# 5. Test addresses and keys are provided for development only
#
# ======================================================